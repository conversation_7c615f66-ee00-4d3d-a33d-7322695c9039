const { validationResult } = require('express-validator');
const AssessmentModel = require('../models/assessmentModel');

/**
 * Assessment Monitoring Controller
 * Handles admin operations for assessment monitoring
 */
class AssessmentController {
  /**
   * Get all assessments with pagination and filtering
   * GET /api/admin/assessments
   */
  static async getAllAssessments(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { 
        page = 1, 
        limit = 20, 
        status = '', 
        user_id = '',
        from_date = '',
        to_date = ''
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);

      const options = {
        limit: parseInt(limit),
        offset,
        status: status.trim(),
        user_id: user_id.trim(),
        from_date: from_date.trim(),
        to_date: to_date.trim()
      };

      const result = await AssessmentModel.getAll(options);

      res.status(200).json({
        success: true,
        message: 'Assessments retrieved successfully',
        data: result.data,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages,
          hasNext: result.page < result.totalPages,
          hasPrev: result.page > 1
        }
      });

    } catch (error) {
      console.error('Get all assessments error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving assessments'
      });
    }
  }

  /**
   * Get assessment by ID
   * GET /api/admin/assessments/:assessmentId
   */
  static async getAssessmentById(req, res) {
    try {
      const { assessmentId } = req.params;

      // Validate UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(assessmentId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid assessment ID format'
        });
      }

      const assessment = await AssessmentModel.getById(assessmentId);

      if (!assessment) {
        return res.status(404).json({
          success: false,
          message: 'Assessment not found'
        });
      }

      res.status(200).json({
        success: true,
        message: 'Assessment retrieved successfully',
        data: assessment
      });

    } catch (error) {
      console.error('Get assessment by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving assessment'
      });
    }
  }

  /**
   * Get assessments by user ID
   * GET /api/admin/assessments/user/:userId
   */
  static async getAssessmentsByUserId(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userId } = req.params;
      const { page = 1, limit = 10 } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      const options = {
        limit: parseInt(limit),
        offset
      };

      const assessments = await AssessmentModel.getByUserId(userId, options);
      const summary = await AssessmentModel.getUserAssessmentSummary(userId);

      res.status(200).json({
        success: true,
        message: 'User assessments retrieved successfully',
        data: {
          assessments,
          summary
        }
      });

    } catch (error) {
      console.error('Get assessments by user ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving user assessments'
      });
    }
  }

  /**
   * Get assessment statistics
   * GET /api/admin/assessments/stats
   */
  static async getAssessmentStats(req, res) {
    try {
      const stats = await AssessmentModel.getStatistics();

      res.status(200).json({
        success: true,
        message: 'Assessment statistics retrieved successfully',
        data: stats
      });

    } catch (error) {
      console.error('Get assessment stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving assessment statistics'
      });
    }
  }

  /**
   * Export assessments data (for future implementation)
   * GET /api/admin/assessments/export
   */
  static async exportAssessments(req, res) {
    try {
      // This is a placeholder for future CSV/Excel export functionality
      res.status(501).json({
        success: false,
        message: 'Export functionality not yet implemented'
      });

    } catch (error) {
      console.error('Export assessments error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during export'
      });
    }
  }
}

module.exports = AssessmentController;
