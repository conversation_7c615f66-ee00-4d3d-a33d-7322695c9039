const express = require('express');
const UserController = require('../../controllers/userController');
const { checkAdmin, requireAdminOrSuperAdmin } = require('../../middleware/checkAdmin');
const { 
  validateUserId, 
  validatePagination, 
  validateUserUpdate, 
  validateUserPlanUpdate 
} = require('../../middleware/validator');

const router = express.Router();

/**
 * User Management Routes
 * Base path: /api/admin/users
 * All routes require admin authentication
 */

// Apply admin authentication to all routes
router.use(checkAdmin);

// Get user statistics
router.get('/stats', requireAdminOrSuperAdmin, UserController.getUserStats);

// Get all users with pagination and search
router.get('/', validatePagination, requireAdminOrSuperAdmin, UserController.getAllUsers);

// Get user by ID
router.get('/:userId', validateUserId, requireAdminOrSuperAdmin, UserController.getUserById);

// Update user information
router.patch('/:userId', 
  validateUserId, 
  validateUserUpdate, 
  requireAdminOrSuperAdmin, 
  UserController.updateUser
);

// Update user plan
router.patch('/:userId/plan', 
  validateUserId, 
  validateUserPlanUpdate, 
  requireAdminOrSuperAdmin, 
  UserController.updateUserPlan
);

module.exports = router;
