const { query } = require('../config/db');

/**
 * Admin Model
 * Handles database operations for admin users
 */
class AdminModel {
  /**
   * Find admin by email
   * @param {string} email - Admin email
   * @returns {Object|null} Admin data or null if not found
   */
  static async findByEmail(email) {
    try {
      const result = await query(
        'SELECT id, email, password_hash, role, nama_lengkap, is_active, last_login_at FROM auth_schema.admins WHERE email = $1 AND is_active = true',
        [email]
      );
      
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error finding admin by email:', error);
      throw error;
    }
  }

  /**
   * Find admin by ID
   * @param {string} adminId - Admin ID
   * @returns {Object|null} Admin data or null if not found
   */
  static async findById(adminId) {
    try {
      const result = await query(
        'SELECT id, email, role, nama_lengkap, is_active, created_at, last_login_at FROM auth_schema.admins WHERE id = $1 AND is_active = true',
        [adminId]
      );
      
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error finding admin by ID:', error);
      throw error;
    }
  }

  /**
   * Update admin last login timestamp
   * @param {string} adminId - Admin ID
   * @returns {boolean} Success status
   */
  static async updateLastLogin(adminId) {
    try {
      await query(
        'UPDATE auth_schema.admins SET last_login_at = NOW() WHERE id = $1',
        [adminId]
      );
      
      return true;
    } catch (error) {
      console.error('Error updating admin last login:', error);
      throw error;
    }
  }

  /**
   * Create new admin
   * @param {Object} adminData - Admin data
   * @returns {Object} Created admin data
   */
  static async create(adminData) {
    try {
      const { email, password_hash, role, nama_lengkap } = adminData;
      
      const result = await query(
        `INSERT INTO auth_schema.admins (email, password_hash, role, nama_lengkap) 
         VALUES ($1, $2, $3, $4) 
         RETURNING id, email, role, nama_lengkap, is_active, created_at`,
        [email, password_hash, role, nama_lengkap]
      );
      
      return result.rows[0];
    } catch (error) {
      console.error('Error creating admin:', error);
      throw error;
    }
  }

  /**
   * Get all admins (for management purposes)
   * @param {Object} options - Query options (limit, offset, etc.)
   * @returns {Object} Admins data with pagination
   */
  static async getAll(options = {}) {
    try {
      const { limit = 20, offset = 0 } = options;
      
      // Get total count
      const countResult = await query(
        'SELECT COUNT(*) FROM auth_schema.admins WHERE is_active = true'
      );
      
      // Get admins
      const result = await query(
        `SELECT id, email, role, nama_lengkap, is_active, created_at, last_login_at 
         FROM auth_schema.admins 
         WHERE is_active = true 
         ORDER BY created_at DESC 
         LIMIT $1 OFFSET $2`,
        [limit, offset]
      );
      
      return {
        data: result.rows,
        total: parseInt(countResult.rows[0].count),
        limit,
        offset
      };
    } catch (error) {
      console.error('Error getting all admins:', error);
      throw error;
    }
  }
}

module.exports = AdminModel;
