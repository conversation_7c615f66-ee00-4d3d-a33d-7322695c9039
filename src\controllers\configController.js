const { validationResult } = require('express-validator');
const ConfigModel = require('../models/configModel');

/**
 * System Configuration Controller
 * Handles admin operations for system configuration management
 */
class ConfigController {
  /**
   * Get all system configurations
   * GET /api/admin/configs
   */
  static async getAllConfigs(req, res) {
    try {
      const { include_sensitive = 'false' } = req.query;
      const includeSensitive = include_sensitive === 'true';

      // Only superadmin can view sensitive configurations
      if (includeSensitive && req.admin.role !== 'superadmin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only superadmin can view sensitive configurations'
        });
      }

      const configs = await ConfigModel.getAll({ includeSensitive });

      // Mask sensitive values for non-superadmin users
      const processedConfigs = configs.map(config => {
        if (config.is_sensitive && req.admin.role !== 'superadmin') {
          return {
            ...config,
            config_value: '***HIDDEN***'
          };
        }
        return config;
      });

      res.status(200).json({
        success: true,
        message: 'System configurations retrieved successfully',
        data: processedConfigs
      });

    } catch (error) {
      console.error('Get all configs error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving configurations'
      });
    }
  }

  /**
   * Get configuration by key
   * GET /api/admin/configs/:configKey
   */
  static async getConfigByKey(req, res) {
    try {
      const { configKey } = req.params;
      const config = await ConfigModel.getByKey(configKey);

      if (!config) {
        return res.status(404).json({
          success: false,
          message: 'Configuration not found'
        });
      }

      // Check if user can view sensitive configuration
      if (config.is_sensitive && req.admin.role !== 'superadmin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. This is a sensitive configuration'
        });
      }

      res.status(200).json({
        success: true,
        message: 'Configuration retrieved successfully',
        data: config
      });

    } catch (error) {
      console.error('Get config by key error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving configuration'
      });
    }
  }

  /**
   * Update system configurations
   * PATCH /api/admin/configs
   */
  static async updateConfigs(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { configs } = req.body;

      // Validate each configuration
      const validationErrors = [];
      for (const config of configs) {
        const { config_key, config_value, config_type = 'string' } = config;

        // Check if configuration exists and if it's sensitive
        const existingConfig = await ConfigModel.getByKey(config_key);
        if (existingConfig && existingConfig.is_sensitive && req.admin.role !== 'superadmin') {
          validationErrors.push({
            config_key,
            message: 'Access denied. Only superadmin can modify sensitive configurations'
          });
          continue;
        }

        // Validate configuration value based on type
        if (!ConfigModel.validateConfigValue(config_value, config_type)) {
          validationErrors.push({
            config_key,
            message: `Invalid value for type ${config_type}`
          });
        }
      }

      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Configuration validation failed',
          errors: validationErrors
        });
      }

      const updatedConfigs = await ConfigModel.updateConfigs(configs);

      // Log admin action
      console.log(`Admin ${req.admin.email} updated configurations:`, 
        configs.map(c => c.config_key).join(', ')
      );

      res.status(200).json({
        success: true,
        message: 'Configurations updated successfully',
        data: updatedConfigs
      });

    } catch (error) {
      console.error('Update configs error:', error);
      
      if (error.code === '23505') { // Unique constraint violation
        return res.status(400).json({
          success: false,
          message: 'Configuration key already exists'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error while updating configurations'
      });
    }
  }

  /**
   * Create new configuration
   * POST /api/admin/configs
   */
  static async createConfig(req, res) {
    try {
      // Only superadmin can create new configurations
      if (req.admin.role !== 'superadmin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only superadmin can create new configurations'
        });
      }

      const configData = req.body;
      const { config_key, config_value, config_type = 'string' } = configData;

      // Validate configuration value
      if (!ConfigModel.validateConfigValue(config_value, config_type)) {
        return res.status(400).json({
          success: false,
          message: `Invalid value for type ${config_type}`
        });
      }

      const newConfig = await ConfigModel.create(configData);

      // Log admin action
      console.log(`Admin ${req.admin.email} created configuration: ${config_key}`);

      res.status(201).json({
        success: true,
        message: 'Configuration created successfully',
        data: newConfig
      });

    } catch (error) {
      console.error('Create config error:', error);
      
      if (error.code === '23505') { // Unique constraint violation
        return res.status(400).json({
          success: false,
          message: 'Configuration key already exists'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error while creating configuration'
      });
    }
  }

  /**
   * Delete configuration
   * DELETE /api/admin/configs/:configKey
   */
  static async deleteConfig(req, res) {
    try {
      // Only superadmin can delete configurations
      if (req.admin.role !== 'superadmin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only superadmin can delete configurations'
        });
      }

      const { configKey } = req.params;
      
      // Check if configuration exists
      const existingConfig = await ConfigModel.getByKey(configKey);
      if (!existingConfig) {
        return res.status(404).json({
          success: false,
          message: 'Configuration not found'
        });
      }

      const deleted = await ConfigModel.deleteByKey(configKey);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          message: 'Configuration not found'
        });
      }

      // Log admin action
      console.log(`Admin ${req.admin.email} deleted configuration: ${configKey}`);

      res.status(200).json({
        success: true,
        message: 'Configuration deleted successfully'
      });

    } catch (error) {
      console.error('Delete config error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while deleting configuration'
      });
    }
  }

  /**
   * Get configuration statistics
   * GET /api/admin/configs/stats
   */
  static async getConfigStats(req, res) {
    try {
      const stats = await ConfigModel.getStatistics();

      res.status(200).json({
        success: true,
        message: 'Configuration statistics retrieved successfully',
        data: stats
      });

    } catch (error) {
      console.error('Get config stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving configuration statistics'
      });
    }
  }
}

module.exports = ConfigController;
