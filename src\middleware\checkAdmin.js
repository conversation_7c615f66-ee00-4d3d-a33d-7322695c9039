const jwt = require('jsonwebtoken');
const AdminModel = require('../models/adminModel');

/**
 * Admin JWT Authentication middleware
 * Verifies admin JWT tokens and ensures admin access
 */
const checkAdmin = async (req, res, next) => {
  try {
    // Check if running in API Gateway mode (headers forwarded)
    const adminId = req.headers['x-admin-id'];
    const adminEmail = req.headers['x-admin-email'];
    const adminRole = req.headers['x-admin-role'];
    
    if (adminId && adminEmail && adminRole) {
      // API Gateway has already validated the token
      req.admin = {
        id: adminId,
        email: adminEmail,
        role: adminRole
      };
      return next();
    }
    
    // Direct access mode - validate JWT ourselves
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Admin access token is required'
      });
    }
    
    try {
      // Verify JWT token with admin secret
      const decoded = jwt.verify(token, process.env.JWT_SECRET_ADMIN);
      
      // Verify admin still exists and is active
      const admin = await AdminModel.findById(decoded.sub);
      if (!admin) {
        return res.status(401).json({
          success: false,
          message: 'Admin account not found or inactive'
        });
      }
      
      // Store admin info in request
      req.admin = {
        id: admin.id,
        email: admin.email,
        role: admin.role,
        nama_lengkap: admin.nama_lengkap
      };
      
      next();
    } catch (jwtError) {
      console.error('Admin JWT verification error:', jwtError);
      
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Admin access token has expired'
        });
      }
      
      if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: 'Invalid admin access token'
        });
      }
      
      return res.status(401).json({
        success: false,
        message: 'Admin token verification failed'
      });
    }
    
  } catch (error) {
    console.error('Admin authentication middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error during authentication'
    });
  }
};

/**
 * Role-based access control middleware
 * Checks if admin has required role
 */
const requireRole = (requiredRoles) => {
  return (req, res, next) => {
    if (!req.admin) {
      return res.status(401).json({
        success: false,
        message: 'Admin authentication required'
      });
    }
    
    const adminRole = req.admin.role;
    const allowedRoles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    
    if (!allowedRoles.includes(adminRole)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required role: ${allowedRoles.join(' or ')}, your role: ${adminRole}`
      });
    }
    
    next();
  };
};

/**
 * Superadmin only access
 */
const requireSuperAdmin = requireRole(['superadmin']);

/**
 * Admin or Superadmin access
 */
const requireAdminOrSuperAdmin = requireRole(['admin', 'superadmin']);

module.exports = {
  checkAdmin,
  requireRole,
  requireSuperAdmin,
  requireAdminOrSuperAdmin
};
