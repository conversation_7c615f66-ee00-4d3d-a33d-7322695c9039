const { Pool } = require('pg');
require('dotenv').config();

// Database connection configuration for Admin Service
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'atma_db',
  user: process.env.DB_USER || 'admin_service_user',
  password: process.env.DB_PASSWORD,
  // Connection pool settings
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 2000, // How long to wait when connecting a new client
};

// Create connection pool
const pool = new Pool(dbConfig);

// Handle pool errors
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Test database connection
const testConnection = async () => {
  try {
    const client = await pool.connect();
    console.log('✅ Admin Service database connected successfully');
    
    // Test access to required schemas
    const testQueries = [
      'SELECT 1 FROM auth_schema.users LIMIT 1',
      'SELECT 1 FROM archive_schema.persona_profiles LIMIT 1',
      'SELECT 1 FROM admin_schema.user_plans LIMIT 1',
      'SELECT 1 FROM admin_schema.system_configs LIMIT 1'
    ];
    
    for (const query of testQueries) {
      try {
        await client.query(query);
      } catch (err) {
        console.warn(`⚠️  Schema access test failed: ${query} - ${err.message}`);
      }
    }
    
    client.release();
  } catch (err) {
    console.error('❌ Admin Service database connection failed:', err.message);
    process.exit(1);
  }
};

// Query helper function with error handling
const query = async (text, params) => {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('Executed query', { text: text.substring(0, 50) + '...', duration, rows: res.rowCount });
    return res;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};

// Get a client from the pool for transactions
const getClient = async () => {
  try {
    const client = await pool.connect();
    return client;
  } catch (error) {
    console.error('Error getting database client:', error);
    throw error;
  }
};

// Transaction helper function
const transaction = async (callback) => {
  const client = await getClient();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

module.exports = {
  pool,
  query,
  getClient,
  transaction,
  testConnection
};
