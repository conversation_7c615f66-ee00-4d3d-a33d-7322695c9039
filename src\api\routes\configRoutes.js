const express = require('express');
const ConfigController = require('../../controllers/configController');
const { checkAdmin, requireSuperAdmin, requireAdminOrSuperAdmin } = require('../../middleware/checkAdmin');
const { validateSystemConfigs } = require('../../middleware/validator');

const router = express.Router();

/**
 * System Configuration Routes
 * Base path: /api/admin/configs
 * All routes require admin authentication
 */

// Apply admin authentication to all routes
router.use(checkAdmin);

// Get configuration statistics
router.get('/stats', requireAdminOrSuperAdmin, ConfigController.getConfigStats);

// Get all configurations
router.get('/', requireAdminOrSuperAdmin, ConfigController.getAllConfigs);

// Create new configuration (superadmin only)
router.post('/', requireSuperAdmin, ConfigController.createConfig);

// Update configurations
router.patch('/', validateSystemConfigs, requireAdminOrSuperAdmin, ConfigController.updateConfigs);

// Get configuration by key
router.get('/:configKey', requireAdminOrSuperAdmin, ConfigController.getConfigByKey);

// Delete configuration (superadmin only)
router.delete('/:configKey', requireSuperAdmin, ConfigController.deleteConfig);

module.exports = router;
