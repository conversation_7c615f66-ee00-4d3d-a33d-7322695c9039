const { query, transaction } = require('../config/db');

/**
 * System Configuration Model
 * Handles database operations for system configurations
 */
class ConfigModel {
  /**
   * Get all system configurations
   * @param {Object} options - Query options
   * @returns {Array} Array of configuration objects
   */
  static async getAll(options = {}) {
    try {
      const { includeSensitive = false } = options;
      
      let whereClause = '';
      if (!includeSensitive) {
        whereClause = 'WHERE is_sensitive = false';
      }

      const result = await query(`
        SELECT 
          id,
          config_key,
          config_value,
          config_type,
          description,
          is_sensitive,
          created_at,
          updated_at
        FROM admin_schema.system_configs
        ${whereClause}
        ORDER BY config_key ASC
      `);

      return result.rows;
    } catch (error) {
      console.error('Error getting all configurations:', error);
      throw error;
    }
  }

  /**
   * Get configuration by key
   * @param {string} configKey - Configuration key
   * @returns {Object|null} Configuration object or null if not found
   */
  static async getBy<PERSON>ey(configKey) {
    try {
      const result = await query(`
        SELECT 
          id,
          config_key,
          config_value,
          config_type,
          description,
          is_sensitive,
          created_at,
          updated_at
        FROM admin_schema.system_configs
        WHERE config_key = $1
      `, [configKey]);

      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting configuration by key:', error);
      throw error;
    }
  }

  /**
   * Update multiple configurations
   * @param {Array} configs - Array of configuration objects
   * @returns {Array} Array of updated configurations
   */
  static async updateConfigs(configs) {
    try {
      return await transaction(async (client) => {
        const updatedConfigs = [];

        for (const config of configs) {
          const { config_key, config_value, config_type, description } = config;

          // Check if configuration exists
          const existingConfig = await client.query(
            'SELECT id, is_sensitive FROM admin_schema.system_configs WHERE config_key = $1',
            [config_key]
          );

          let result;

          if (existingConfig.rows.length > 0) {
            // Update existing configuration
            const updateFields = ['config_value = $2'];
            const updateValues = [config_key, config_value];
            let paramIndex = 3;

            if (config_type !== undefined) {
              updateFields.push(`config_type = $${paramIndex}`);
              updateValues.push(config_type);
              paramIndex++;
            }

            if (description !== undefined) {
              updateFields.push(`description = $${paramIndex}`);
              updateValues.push(description);
              paramIndex++;
            }

            const updateQuery = `
              UPDATE admin_schema.system_configs 
              SET ${updateFields.join(', ')}, updated_at = NOW()
              WHERE config_key = $1
              RETURNING *
            `;

            result = await client.query(updateQuery, updateValues);
          } else {
            // Create new configuration
            const insertQuery = `
              INSERT INTO admin_schema.system_configs (config_key, config_value, config_type, description)
              VALUES ($1, $2, $3, $4)
              RETURNING *
            `;

            result = await client.query(insertQuery, [
              config_key,
              config_value,
              config_type || 'string',
              description || null
            ]);
          }

          updatedConfigs.push(result.rows[0]);
        }

        return updatedConfigs;
      });
    } catch (error) {
      console.error('Error updating configurations:', error);
      throw error;
    }
  }

  /**
   * Create new configuration
   * @param {Object} configData - Configuration data
   * @returns {Object} Created configuration
   */
  static async create(configData) {
    try {
      const { config_key, config_value, config_type, description, is_sensitive } = configData;

      const result = await query(`
        INSERT INTO admin_schema.system_configs (config_key, config_value, config_type, description, is_sensitive)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, [config_key, config_value, config_type || 'string', description, is_sensitive || false]);

      return result.rows[0];
    } catch (error) {
      console.error('Error creating configuration:', error);
      throw error;
    }
  }

  /**
   * Delete configuration by key
   * @param {string} configKey - Configuration key
   * @returns {boolean} Success status
   */
  static async deleteByKey(configKey) {
    try {
      const result = await query(`
        DELETE FROM admin_schema.system_configs
        WHERE config_key = $1
        RETURNING id
      `, [configKey]);

      return result.rows.length > 0;
    } catch (error) {
      console.error('Error deleting configuration:', error);
      throw error;
    }
  }

  /**
   * Get configuration statistics
   * @returns {Object} Configuration statistics
   */
  static async getStatistics() {
    try {
      const result = await query(`
        SELECT 
          COUNT(*) as total_configs,
          COUNT(CASE WHEN config_type = 'string' THEN 1 END) as string_configs,
          COUNT(CASE WHEN config_type = 'number' THEN 1 END) as number_configs,
          COUNT(CASE WHEN config_type = 'boolean' THEN 1 END) as boolean_configs,
          COUNT(CASE WHEN config_type = 'json' THEN 1 END) as json_configs,
          COUNT(CASE WHEN is_sensitive = true THEN 1 END) as sensitive_configs,
          COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as updated_24h
        FROM admin_schema.system_configs
      `);

      return result.rows[0];
    } catch (error) {
      console.error('Error getting configuration statistics:', error);
      throw error;
    }
  }

  /**
   * Validate configuration value based on type
   * @param {string} value - Configuration value
   * @param {string} type - Configuration type
   * @returns {boolean} Validation result
   */
  static validateConfigValue(value, type) {
    try {
      switch (type) {
        case 'number':
          return !isNaN(parseFloat(value)) && isFinite(value);
        case 'boolean':
          return value === 'true' || value === 'false';
        case 'json':
          try {
            JSON.parse(value);
            return true;
          } catch {
            return false;
          }
        case 'string':
        default:
          return typeof value === 'string';
      }
    } catch (error) {
      return false;
    }
  }

  /**
   * Parse configuration value based on type
   * @param {string} value - Configuration value
   * @param {string} type - Configuration type
   * @returns {any} Parsed value
   */
  static parseConfigValue(value, type) {
    try {
      switch (type) {
        case 'number':
          return parseFloat(value);
        case 'boolean':
          return value === 'true';
        case 'json':
          return JSON.parse(value);
        case 'string':
        default:
          return value;
      }
    } catch (error) {
      return value;
    }
  }
}

module.exports = ConfigModel;
