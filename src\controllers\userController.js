const { validationResult } = require('express-validator');
const UserModel = require('../models/userModel');

/**
 * User Management Controller
 * Handles admin operations for user management
 */
class UserController {
  /**
   * Get all users with pagination and search
   * GET /api/admin/users
   */
  static async getAllUsers(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { page = 1, limit = 20, search = '' } = req.query;
      const offset = (parseInt(page) - 1) * parseInt(limit);

      const options = {
        limit: parseInt(limit),
        offset,
        search: search.trim()
      };

      const result = await UserModel.getAll(options);

      res.status(200).json({
        success: true,
        message: 'Users retrieved successfully',
        data: result.data,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages,
          hasNext: result.page < result.totalPages,
          hasPrev: result.page > 1
        }
      });

    } catch (error) {
      console.error('Get all users error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving users'
      });
    }
  }

  /**
   * Get user by ID
   * GET /api/admin/users/:userId
   */
  static async getUserById(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userId } = req.params;
      const user = await UserModel.getById(userId);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.status(200).json({
        success: true,
        message: 'User retrieved successfully',
        data: user
      });

    } catch (error) {
      console.error('Get user by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving user'
      });
    }
  }

  /**
   * Update user information
   * PATCH /api/admin/users/:userId
   */
  static async updateUser(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userId } = req.params;
      const updateData = req.body;

      // Remove undefined/null values
      const cleanedData = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined && value !== null)
      );

      if (Object.keys(cleanedData).length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No valid fields provided for update'
        });
      }

      const updatedUser = await UserModel.updateUser(userId, cleanedData);

      // Log admin action
      console.log(`Admin ${req.admin.email} updated user ${userId}:`, cleanedData);

      res.status(200).json({
        success: true,
        message: 'User updated successfully',
        data: updatedUser
      });

    } catch (error) {
      console.error('Update user error:', error);
      
      if (error.message === 'User not found') {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      if (error.message === 'No valid fields to update') {
        return res.status(400).json({
          success: false,
          message: 'No valid fields provided for update'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error while updating user'
      });
    }
  }

  /**
   * Update user plan
   * PATCH /api/admin/users/:userId/plan
   */
  static async updateUserPlan(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userId } = req.params;
      const planData = req.body;

      // Remove undefined/null values
      const cleanedData = Object.fromEntries(
        Object.entries(planData).filter(([_, value]) => value !== undefined && value !== null)
      );

      if (Object.keys(cleanedData).length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No valid fields provided for plan update'
        });
      }

      const updatedPlan = await UserModel.updateUserPlan(userId, cleanedData);

      // Log admin action
      console.log(`Admin ${req.admin.email} updated plan for user ${userId}:`, cleanedData);

      res.status(200).json({
        success: true,
        message: 'User plan updated successfully',
        data: updatedPlan
      });

    } catch (error) {
      console.error('Update user plan error:', error);
      
      if (error.message === 'User not found') {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      if (error.message === 'No valid fields to update') {
        return res.status(400).json({
          success: false,
          message: 'No valid fields provided for plan update'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error while updating user plan'
      });
    }
  }

  /**
   * Get user statistics
   * GET /api/admin/users/stats
   */
  static async getUserStats(req, res) {
    try {
      const stats = await UserModel.getStatistics();

      res.status(200).json({
        success: true,
        message: 'User statistics retrieved successfully',
        data: stats
      });

    } catch (error) {
      console.error('Get user stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while retrieving user statistics'
      });
    }
  }
}

module.exports = UserController;
