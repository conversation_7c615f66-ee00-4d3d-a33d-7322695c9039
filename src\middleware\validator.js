const { body, query, param } = require('express-validator');

/**
 * Validation middleware for Admin Service endpoints
 */

// Admin login validation
const validateAdminLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
];

// User ID parameter validation
const validateUserId = [
  param('userId')
    .isUUID()
    .withMessage('Valid user ID is required')
];

// Pagination validation
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Search term must be between 1 and 255 characters')
];

// User update validation
const validateUserUpdate = [
  body('nama_lengkap')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Full name must be between 2 and 255 characters'),
  body('asal_sekolah')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('School name must be between 2 and 255 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required')
];

// User plan update validation
const validateUserPlanUpdate = [
  body('plan_type')
    .optional()
    .isIn(['free', 'premium', 'enterprise'])
    .withMessage('Plan type must be free, premium, or enterprise'),
  body('analysis_quota')
    .optional()
    .isInt({ min: 0, max: 10000 })
    .withMessage('Analysis quota must be between 0 and 10000'),
  body('used_quota')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Used quota must be a non-negative integer'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean')
];

// Assessment query validation
const validateAssessmentQuery = [
  query('status')
    .optional()
    .isIn(['processing', 'completed', 'failed'])
    .withMessage('Status must be processing, completed, or failed'),
  query('user_id')
    .optional()
    .isUUID()
    .withMessage('User ID must be a valid UUID'),
  query('from_date')
    .optional()
    .isISO8601()
    .withMessage('From date must be a valid ISO 8601 date'),
  query('to_date')
    .optional()
    .isISO8601()
    .withMessage('To date must be a valid ISO 8601 date')
];

// System config validation
const validateSystemConfigs = [
  body('configs')
    .isArray({ min: 1 })
    .withMessage('Configs must be a non-empty array'),
  body('configs.*.config_key')
    .isLength({ min: 1, max: 255 })
    .withMessage('Config key must be between 1 and 255 characters'),
  body('configs.*.config_value')
    .isLength({ min: 0, max: 10000 })
    .withMessage('Config value must be at most 10000 characters'),
  body('configs.*.config_type')
    .optional()
    .isIn(['string', 'number', 'boolean', 'json'])
    .withMessage('Config type must be string, number, boolean, or json'),
  body('configs.*.description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be at most 1000 characters')
];

// Admin creation validation (for future use)
const validateAdminCreation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must be at least 8 characters with uppercase, lowercase, and number'),
  body('nama_lengkap')
    .isLength({ min: 2, max: 255 })
    .withMessage('Full name must be between 2 and 255 characters'),
  body('role')
    .isIn(['admin', 'superadmin', 'support', 'viewer'])
    .withMessage('Role must be admin, superadmin, support, or viewer')
];

module.exports = {
  validateAdminLogin,
  validateUserId,
  validatePagination,
  validateUserUpdate,
  validateUserPlanUpdate,
  validateAssessmentQuery,
  validateSystemConfigs,
  validateAdminCreation
};
