const express = require('express');
const adminRoutes = require('./api/routes');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware for parsing JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('X-Service-Name', 'admin-service');
  next();
});

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} - ${req.method} ${req.path} - IP: ${req.ip}`);
  next();
});

// API routes
app.use('/api/admin', adminRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'ATMA Admin Service API (Test Mode)',
    version: '1.0.0',
    description: 'Administrative backend for ATMA platform management',
    note: 'Running in test mode without database connection',
    features: [
      'Admin Authentication',
      'User Management',
      'Assessment Monitoring', 
      'System Configuration'
    ],
    endpoints: {
      health: '/api/admin/health',
      documentation: '/api/admin/',
      login: 'POST /api/admin/auth/login'
    },
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
    availableEndpoints: [
      'GET /',
      'GET /api/admin/health',
      'GET /api/admin/',
      'POST /api/admin/auth/login'
    ]
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);
  
  // Handle specific error types
  if (err.type === 'entity.parse.failed') {
    return res.status(400).json({
      success: false,
      message: 'Invalid JSON in request body'
    });
  }
  
  if (err.type === 'entity.too.large') {
    return res.status(413).json({
      success: false,
      message: 'Request body too large'
    });
  }

  // Handle JWT errors
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid or expired admin token'
    });
  }
  
  // Default error response
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { 
      stack: err.stack,
      details: err
    })
  });
});

// Start server without database connection
const startTestServer = async () => {
  try {
    console.log('🧪 Starting Admin Service in TEST MODE (no database)');
    
    // Start listening
    const server = app.listen(PORT, () => {
      console.log(`🚀 Admin Service (TEST) running on port ${PORT}`);
      console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 Health check: http://localhost:${PORT}/api/admin/health`);
      console.log(`📚 API docs: http://localhost:${PORT}/api/admin/`);
      console.log(`⚠️  NOTE: Database connection disabled for testing`);
      console.log(`⚡ Service ready for basic endpoint testing`);
    });

    // Handle server errors
    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
        process.exit(1);
      } else {
        console.error('❌ Server error:', error);
        process.exit(1);
      }
    });
    
  } catch (error) {
    console.error('❌ Failed to start Admin Service (TEST):', error);
    process.exit(1);
  }
};

// Start the test server
startTestServer();

module.exports = app;
