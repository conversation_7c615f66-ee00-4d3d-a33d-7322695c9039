# Admin Service API Documentation

## Overview

The Admin Service provides administrative backend capabilities for the ATMA platform, including user management, assessment monitoring, and system configuration.

**Base URL:** `http://localhost:3004/api/admin`

## Authentication

All endpoints (except login) require admin authentication using JWT Bearer tokens.

```
Authorization: Bearer <admin_token>
```

Get admin token from the login endpoint.

## Endpoints

### Authentication

#### POST /auth/login
Admin login to obtain access token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Admin login successful",
  "data": {
    "adminAccessToken": "jwt.token.here",
    "admin": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "superadmin",
      "nama_lengkap": "System Administrator"
    }
  }
}
```

#### GET /auth/me
Get current admin profile.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "role": "superadmin",
    "nama_lengkap": "System Administrator",
    "created_at": "2024-01-01T00:00:00.000Z",
    "last_login_at": "2024-01-01T12:00:00.000Z"
  }
}
```

#### POST /auth/logout
Admin logout.

**Response:**
```json
{
  "success": true,
  "message": "Admin logout successful"
}
```

### User Management

#### GET /users
Get all users with pagination and search.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `search` (optional): Search term for name, email, or school

**Response:**
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": [
    {
      "id": "uuid",
      "email": "<EMAIL>",
      "nama_lengkap": "User Name",
      "asal_sekolah": "School Name",
      "created_at": "2024-01-01T00:00:00.000Z",
      "plan_type": "free",
      "analysis_quota": 5,
      "used_quota": 2
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### GET /users/stats
Get user statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "total_users": "150",
    "free_users": "120",
    "premium_users": "25",
    "enterprise_users": "5",
    "new_users_30d": "15"
  }
}
```

#### GET /users/:userId
Get user details by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "nama_lengkap": "User Name",
    "asal_sekolah": "School Name",
    "created_at": "2024-01-01T00:00:00.000Z",
    "plan_id": "uuid",
    "plan_type": "premium",
    "analysis_quota": 50,
    "used_quota": 10,
    "plan_active": true
  }
}
```

#### PATCH /users/:userId
Update user information.

**Request:**
```json
{
  "nama_lengkap": "New Name",
  "asal_sekolah": "New School",
  "email": "<EMAIL>"
}
```

#### PATCH /users/:userId/plan
Update user subscription plan.

**Request:**
```json
{
  "plan_type": "premium",
  "analysis_quota": 50,
  "used_quota": 0,
  "is_active": true
}
```

### Assessment Monitoring

#### GET /assessments
Get all assessments with filtering.

**Query Parameters:**
- `page`, `limit`: Pagination
- `status`: Filter by status (processing, completed, failed)
- `user_id`: Filter by user ID
- `from_date`, `to_date`: Date range filter (ISO 8601)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "user_id": "uuid",
      "status": "completed",
      "persona_result": {...},
      "created_at": "2024-01-01T00:00:00.000Z",
      "completed_at": "2024-01-01T00:05:00.000Z",
      "user_email": "<EMAIL>",
      "user_name": "User Name"
    }
  ],
  "pagination": {...}
}
```

#### GET /assessments/stats
Get assessment statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "total_assessments": "500",
    "completed_assessments": "450",
    "processing_assessments": "10",
    "failed_assessments": "40",
    "assessments_24h": "25",
    "avg_processing_time_seconds": "45.5",
    "daily_stats": [...]
  }
}
```

#### GET /assessments/:assessmentId
Get assessment details by ID.

#### GET /assessments/user/:userId
Get assessments for specific user.

### System Configuration

#### GET /configs
Get all system configurations.

**Query Parameters:**
- `include_sensitive`: Include sensitive configs (superadmin only)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "config_key": "llm_model",
      "config_value": "gpt-4",
      "config_type": "string",
      "description": "Default LLM model",
      "is_sensitive": false,
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### PATCH /configs
Update multiple configurations.

**Request:**
```json
{
  "configs": [
    {
      "config_key": "llm_model",
      "config_value": "gpt-4-turbo",
      "config_type": "string",
      "description": "Updated LLM model"
    }
  ]
}
```

#### POST /configs
Create new configuration (superadmin only).

#### DELETE /configs/:configKey
Delete configuration (superadmin only).

## Error Responses

All endpoints return errors in this format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [...] // For validation errors
}
```

**Common HTTP Status Codes:**
- `200`: Success
- `201`: Created
- `400`: Bad Request / Validation Error
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Role-Based Access

- **superadmin**: Full access to all endpoints
- **admin**: Access to user management and assessments, limited config access
- **support**: Read-only access to users and assessments
- **viewer**: Read-only access to statistics

## Rate Limiting

API requests are logged but not currently rate-limited. Consider implementing rate limiting in production.
