const express = require('express');
const AuthController = require('../../controllers/authController');
const { checkAdmin } = require('../../middleware/checkAdmin');
const { validateAdminLogin } = require('../../middleware/validator');

const router = express.Router();

/**
 * Admin Authentication Routes
 * Base path: /api/admin/auth
 */

// Health check
router.get('/health', AuthController.health);

// Admin login (no authentication required)
router.post('/login', validateAdminLogin, AuthController.login);

// Admin logout (requires authentication)
router.post('/logout', checkAdmin, AuthController.logout);

// Get current admin profile (requires authentication)
router.get('/me', checkAdmin, AuthController.getProfile);

module.exports = router;
