# Admin Service

Admin microservice for ATMA backend that provides administrative dashboard backend capabilities.

## Features

- **Admin Authentication**: Secure login system for administrators with separate JWT tokens
- **User Management**: Complete CRUD operations for user data and subscription plans
- **Assessment Monitoring**: View and monitor all assessment results across the platform
- **System Configuration**: Dynamic system configuration management without code deployment

## API Endpoints

### Authentication
- `POST /api/admin/auth/login` - Admin login

### User Management
- `GET /api/admin/users` - List all users (with pagination/search)
- `GET /api/admin/users/:userId` - Get user details
- `PATCH /api/admin/users/:userId` - Update user information
- `PATCH /api/admin/users/:userId/plan` - Update user subscription plan

### Assessment Monitoring
- `GET /api/admin/assessments` - View all assessment results

### System Configuration
- `GET /api/admin/configs` - Get system configurations
- `PATCH /api/admin/configs` - Update system configurations

## Setup

1. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up database (run init-db.sql in your PostgreSQL database)

4. Start the service:
   ```bash
   npm run dev
   ```

## Database Access

This service requires access to:
- `auth_schema.users` (SELECT, UPDATE)
- `auth_schema.admins` (SELECT, INSERT)
- `archive_schema.persona_profiles` (SELECT)
- `admin_schema.user_plans` (SELECT, INSERT, UPDATE, DELETE)
- `admin_schema.system_configs` (SELECT, INSERT, UPDATE, DELETE)

## Security

- Uses separate JWT secret for admin authentication
- Implements role-based access control
- All admin actions should be logged for audit purposes
- Input validation on all endpoints
