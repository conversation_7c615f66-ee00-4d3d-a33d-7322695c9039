const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');
const AdminModel = require('../models/adminModel');

/**
 * Admin Authentication Controller
 * Handles admin login and authentication
 */
class AuthController {
  /**
   * Admin login
   * POST /api/admin/auth/login
   */
  static async login(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, password } = req.body;

      // Find admin by email
      const admin = await AdminModel.findByEmail(email);
      if (!admin) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, admin.password_hash);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Generate JWT token
      const tokenPayload = {
        sub: admin.id,
        email: admin.email,
        role: admin.role,
        type: 'admin'
      };

      const adminAccessToken = jwt.sign(
        tokenPayload,
        process.env.JWT_SECRET_ADMIN,
        { 
          expiresIn: '8h',
          issuer: 'atma-admin-service'
        }
      );

      // Update last login timestamp
      await AdminModel.updateLastLogin(admin.id);

      // Log successful login
      console.log(`Admin login successful: ${admin.email} (${admin.role})`);

      // Return success response
      res.status(200).json({
        success: true,
        message: 'Admin login successful',
        data: {
          adminAccessToken,
          admin: {
            id: admin.id,
            email: admin.email,
            role: admin.role,
            nama_lengkap: admin.nama_lengkap,
            last_login_at: new Date().toISOString()
          }
        }
      });

    } catch (error) {
      console.error('Admin login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during login'
      });
    }
  }

  /**
   * Get current admin profile
   * GET /api/admin/auth/me
   */
  static async getProfile(req, res) {
    try {
      const adminId = req.admin.id;
      
      const admin = await AdminModel.findById(adminId);
      if (!admin) {
        return res.status(404).json({
          success: false,
          message: 'Admin not found'
        });
      }

      res.status(200).json({
        success: true,
        data: {
          id: admin.id,
          email: admin.email,
          role: admin.role,
          nama_lengkap: admin.nama_lengkap,
          created_at: admin.created_at,
          last_login_at: admin.last_login_at
        }
      });

    } catch (error) {
      console.error('Get admin profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Admin logout (token invalidation would be handled by client)
   * POST /api/admin/auth/logout
   */
  static async logout(req, res) {
    try {
      // In a stateless JWT system, logout is typically handled client-side
      // by removing the token. For enhanced security, you could implement
      // a token blacklist here.
      
      console.log(`Admin logout: ${req.admin.email}`);
      
      res.status(200).json({
        success: true,
        message: 'Admin logout successful'
      });

    } catch (error) {
      console.error('Admin logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during logout'
      });
    }
  }

  /**
   * Health check for auth endpoints
   * GET /api/admin/auth/health
   */
  static async health(req, res) {
    res.status(200).json({
      success: true,
      message: 'Admin Auth Service is healthy',
      timestamp: new Date().toISOString(),
      service: 'admin-auth'
    });
  }
}

module.exports = AuthController;
