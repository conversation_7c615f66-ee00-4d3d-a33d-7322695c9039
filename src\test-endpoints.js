const http = require('http');
require('dotenv').config();

const BASE_URL = `http://localhost:${process.env.PORT || 3004}`;

/**
 * Test suite for Admin Service endpoints
 * This script tests all major functionality of the Admin Service
 */

let adminToken = '';

// Test utilities
const makeRequest = (options, data = null) => {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response, headers: res.headers });
        } catch (error) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
};

const test = async (name, testFn) => {
  try {
    console.log(`\n🧪 Testing: ${name}`);
    await testFn();
    console.log(`✅ ${name} - PASSED`);
  } catch (error) {
    console.log(`❌ ${name} - FAILED: ${error.message}`);
  }
};

// Test functions
const testHealthCheck = async () => {
  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/health',
    method: 'GET'
  };

  const response = await makeRequest(options);
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Health check failed');
  }
};

const testRootEndpoint = async () => {
  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/',
    method: 'GET'
  };

  const response = await makeRequest(options);
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Root endpoint failed');
  }
};

const testAdminLogin = async () => {
  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const loginData = {
    email: '<EMAIL>',
    password: 'admin123'
  };

  const response = await makeRequest(options, loginData);
  
  if (response.status !== 200) {
    throw new Error(`Login failed with status ${response.status}: ${JSON.stringify(response.data)}`);
  }
  
  if (!response.data.success || !response.data.data.adminAccessToken) {
    throw new Error('Login response invalid');
  }

  adminToken = response.data.data.adminAccessToken;
  console.log(`   🔑 Admin token obtained: ${adminToken.substring(0, 20)}...`);
};

const testAdminProfile = async () => {
  if (!adminToken) {
    throw new Error('Admin token not available');
  }

  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/auth/me',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  };

  const response = await makeRequest(options);
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Get profile failed');
  }

  console.log(`   👤 Admin profile: ${response.data.data.email} (${response.data.data.role})`);
};

const testGetUsers = async () => {
  if (!adminToken) {
    throw new Error('Admin token not available');
  }

  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/users?page=1&limit=5',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  };

  const response = await makeRequest(options);
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}: ${JSON.stringify(response.data)}`);
  }
  
  if (!response.data.success) {
    throw new Error('Get users failed');
  }

  console.log(`   👥 Found ${response.data.data.length} users`);
};

const testGetUserStats = async () => {
  if (!adminToken) {
    throw new Error('Admin token not available');
  }

  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/users/stats',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  };

  const response = await makeRequest(options);
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Get user stats failed');
  }

  console.log(`   📊 User stats: ${response.data.data.total_users} total users`);
};

const testGetAssessments = async () => {
  if (!adminToken) {
    throw new Error('Admin token not available');
  }

  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/assessments?page=1&limit=5',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  };

  const response = await makeRequest(options);
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Get assessments failed');
  }

  console.log(`   📋 Found ${response.data.data.length} assessments`);
};

const testGetConfigs = async () => {
  if (!adminToken) {
    throw new Error('Admin token not available');
  }

  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/configs',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  };

  const response = await makeRequest(options);
  
  if (response.status !== 200) {
    throw new Error(`Expected status 200, got ${response.status}`);
  }
  
  if (!response.data.success) {
    throw new Error('Get configs failed');
  }

  console.log(`   ⚙️  Found ${response.data.data.length} configurations`);
};

const testUnauthorizedAccess = async () => {
  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/users',
    method: 'GET'
    // No authorization header
  };

  const response = await makeRequest(options);
  
  if (response.status !== 401) {
    throw new Error(`Expected status 401, got ${response.status}`);
  }
};

const testInvalidEndpoint = async () => {
  const options = {
    hostname: 'localhost',
    port: process.env.PORT || 3004,
    path: '/api/admin/nonexistent',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  };

  const response = await makeRequest(options);
  
  if (response.status !== 404) {
    throw new Error(`Expected status 404, got ${response.status}`);
  }
};

// Main test runner
const runTests = async () => {
  console.log('🚀 Starting Admin Service Test Suite');
  console.log(`📍 Testing against: ${BASE_URL}`);
  
  // Basic connectivity tests
  await test('Health Check', testHealthCheck);
  await test('Root Endpoint', testRootEndpoint);
  
  // Authentication tests
  await test('Admin Login', testAdminLogin);
  await test('Admin Profile', testAdminProfile);
  
  // Authorized endpoint tests
  await test('Get Users', testGetUsers);
  await test('Get User Stats', testGetUserStats);
  await test('Get Assessments', testGetAssessments);
  await test('Get Configurations', testGetConfigs);
  
  // Security tests
  await test('Unauthorized Access', testUnauthorizedAccess);
  await test('Invalid Endpoint', testInvalidEndpoint);
  
  console.log('\n🎉 Test suite completed!');
  console.log('💡 For more detailed testing, consider using a proper testing framework like Jest or Mocha');
};

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
