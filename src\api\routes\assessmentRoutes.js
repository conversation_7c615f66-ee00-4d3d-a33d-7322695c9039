const express = require('express');
const AssessmentController = require('../../controllers/assessmentController');
const { checkAdmin, requireAdminOrSuperAdmin } = require('../../middleware/checkAdmin');
const { 
  validateUserId, 
  validatePagination, 
  validateAssessmentQuery 
} = require('../../middleware/validator');

const router = express.Router();

/**
 * Assessment Monitoring Routes
 * Base path: /api/admin/assessments
 * All routes require admin authentication
 */

// Apply admin authentication to all routes
router.use(checkAdmin);

// Get assessment statistics
router.get('/stats', requireAdminOrSuperAdmin, AssessmentController.getAssessmentStats);

// Export assessments (future feature)
router.get('/export', requireAdminOrSuperAdmin, AssessmentController.exportAssessments);

// Get assessments by user ID
router.get('/user/:userId', 
  validateUserId, 
  validatePagination, 
  requireAdminOrSuperAdmin, 
  AssessmentController.getAssessmentsByUserId
);

// Get all assessments with filtering
router.get('/', 
  validatePagination, 
  validateAssessmentQuery, 
  requireAdminOrSuperAdmin, 
  AssessmentController.getAllAssessments
);

// Get assessment by ID
router.get('/:assessmentId', 
  requireAdminOrSuperAdmin, 
  AssessmentController.getAssessmentById
);

module.exports = router;
