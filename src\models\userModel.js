const { query, transaction } = require('../config/db');

/**
 * User Model for Admin Service
 * Handles database operations for user management
 */
class UserModel {
  /**
   * Get all users with pagination and search
   * @param {Object} options - Query options
   * @returns {Object} Users data with pagination
   */
  static async getAll(options = {}) {
    try {
      const {
        limit = 20,
        offset = 0,
        search = '',
        orderBy = 'created_at',
        orderDirection = 'DESC'
      } = options;

      let whereClause = '';
      let queryParams = [limit, offset];
      let paramIndex = 3;

      // Add search functionality
      if (search) {
        whereClause = `WHERE (u.nama_lengkap ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex} OR u.asal_sekolah ILIKE $${paramIndex})`;
        queryParams.push(`%${search}%`);
        paramIndex++;
      }

      // Get total count
      const countQuery = `
        SELECT COUNT(*)
        FROM auth_schema.users u
        ${whereClause}
      `;
      const countResult = await query(countQuery, search ? [queryParams[2]] : []);

      // Get users with their plans
      const usersQuery = `
        SELECT
          u.id,
          u.email,
          u.nama_lengkap,
          u.asal_sekolah,
          u.created_at,
          u.updated_at,
          up.plan_type,
          up.analysis_quota,
          up.used_quota,
          up.quota_reset_date,
          up.is_active as plan_active
        FROM auth_schema.users u
        LEFT JOIN admin_schema.user_plans up ON u.id = up.user_id
        ${whereClause}
        ORDER BY u.${orderBy} ${orderDirection}
        LIMIT $1 OFFSET $2
      `;

      const result = await query(usersQuery, queryParams);

      return {
        data: result.rows,
        total: parseInt(countResult.rows[0].count),
        limit,
        offset,
        page: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit)
      };
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  /**
   * Get user by ID with plan details
   * @param {string} userId - User ID
   * @returns {Object|null} User data or null if not found
   */
  static async getById(userId) {
    try {
      const result = await query(`
        SELECT 
          u.id,
          u.email,
          u.nama_lengkap,
          u.asal_sekolah,
          u.created_at,
          u.updated_at,
          up.id as plan_id,
          up.plan_type,
          up.analysis_quota,
          up.used_quota,
          up.quota_reset_date,
          up.is_active as plan_active,
          up.created_at as plan_created_at,
          up.updated_at as plan_updated_at
        FROM auth_schema.users u
        LEFT JOIN admin_schema.user_plans up ON u.id = up.user_id
        WHERE u.id = $1
      `, [userId]);

      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      throw error;
    }
  }

  /**
   * Update user information
   * @param {string} userId - User ID
   * @param {Object} userData - User data to update
   * @returns {Object} Updated user data
   */
  static async updateUser(userId, userData) {
    try {
      const allowedFields = ['nama_lengkap', 'asal_sekolah', 'email'];
      const updates = [];
      const values = [];
      let paramIndex = 1;

      // Build dynamic update query
      for (const [key, value] of Object.entries(userData)) {
        if (allowedFields.includes(key) && value !== undefined) {
          updates.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      }

      if (updates.length === 0) {
        throw new Error('No valid fields to update');
      }

      values.push(userId); // Add userId as last parameter

      const updateQuery = `
        UPDATE auth_schema.users 
        SET ${updates.join(', ')}, updated_at = NOW()
        WHERE id = $${paramIndex}
        RETURNING id, email, nama_lengkap, asal_sekolah, updated_at
      `;

      const result = await query(updateQuery, values);
      
      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      return result.rows[0];
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Update or create user plan
   * @param {string} userId - User ID
   * @param {Object} planData - Plan data to update
   * @returns {Object} Updated plan data
   */
  static async updateUserPlan(userId, planData) {
    try {
      return await transaction(async (client) => {
        // Check if user exists
        const userCheck = await client.query(
          'SELECT id FROM auth_schema.users WHERE id = $1',
          [userId]
        );

        if (userCheck.rows.length === 0) {
          throw new Error('User not found');
        }

        // Check if plan exists
        const existingPlan = await client.query(
          'SELECT id FROM admin_schema.user_plans WHERE user_id = $1',
          [userId]
        );

        const allowedFields = ['plan_type', 'analysis_quota', 'used_quota', 'is_active'];
        const updates = [];
        const values = [];
        let paramIndex = 1;

        // Build dynamic update/insert query
        for (const [key, value] of Object.entries(planData)) {
          if (allowedFields.includes(key) && value !== undefined) {
            updates.push(key);
            values.push(value);
            paramIndex++;
          }
        }

        if (updates.length === 0) {
          throw new Error('No valid fields to update');
        }

        let result;

        if (existingPlan.rows.length > 0) {
          // Update existing plan
          const setClause = updates.map((field, index) => `${field} = $${index + 1}`).join(', ');
          values.push(userId);

          const updateQuery = `
            UPDATE admin_schema.user_plans 
            SET ${setClause}, updated_at = NOW()
            WHERE user_id = $${paramIndex}
            RETURNING *
          `;

          result = await client.query(updateQuery, values);
        } else {
          // Create new plan
          const defaultPlan = {
            plan_type: 'free',
            analysis_quota: 5,
            used_quota: 0,
            is_active: true,
            ...Object.fromEntries(updates.map((field, index) => [field, values[index]]))
          };

          const insertQuery = `
            INSERT INTO admin_schema.user_plans (user_id, plan_type, analysis_quota, used_quota, is_active)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
          `;

          result = await client.query(insertQuery, [
            userId,
            defaultPlan.plan_type,
            defaultPlan.analysis_quota,
            defaultPlan.used_quota,
            defaultPlan.is_active
          ]);
        }

        return result.rows[0];
      });
    } catch (error) {
      console.error('Error updating user plan:', error);
      throw error;
    }
  }

  /**
   * Get user statistics
   * @returns {Object} User statistics
   */
  static async getStatistics() {
    try {
      const stats = await query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN up.plan_type = 'free' THEN 1 END) as free_users,
          COUNT(CASE WHEN up.plan_type = 'premium' THEN 1 END) as premium_users,
          COUNT(CASE WHEN up.plan_type = 'enterprise' THEN 1 END) as enterprise_users,
          COUNT(CASE WHEN u.created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as new_users_30d
        FROM auth_schema.users u
        LEFT JOIN admin_schema.user_plans up ON u.id = up.user_id
      `);

      return stats.rows[0];
    } catch (error) {
      console.error('Error getting user statistics:', error);
      throw error;
    }
  }
}

module.exports = UserModel;
