const express = require('express');
const authRoutes = require('./authRoutes');
const userRoutes = require('./userRoutes');
const assessmentRoutes = require('./assessmentRoutes');
const configRoutes = require('./configRoutes');

const router = express.Router();

/**
 * Main API Routes for Admin Service
 * Base path: /api/admin
 */

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Admin Service is healthy',
    timestamp: new Date().toISOString(),
    service: 'admin-service',
    version: '1.0.0'
  });
});

// Authentication routes (login, logout, profile)
router.use('/auth', authRoutes);

// User management routes
router.use('/users', userRoutes);

// Assessment monitoring routes
router.use('/assessments', assessmentRoutes);

// System configuration routes
router.use('/configs', configRoutes);

// API documentation endpoint
router.get('/', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Admin Service API',
    version: '1.0.0',
    description: 'Administrative backend for ATMA platform',
    endpoints: {
      health: 'GET /api/admin/health',
      auth: {
        login: 'POST /api/admin/auth/login',
        logout: 'POST /api/admin/auth/logout',
        profile: 'GET /api/admin/auth/me'
      },
      users: {
        list: 'GET /api/admin/users',
        stats: 'GET /api/admin/users/stats',
        detail: 'GET /api/admin/users/:userId',
        update: 'PATCH /api/admin/users/:userId',
        updatePlan: 'PATCH /api/admin/users/:userId/plan'
      },
      assessments: {
        list: 'GET /api/admin/assessments',
        stats: 'GET /api/admin/assessments/stats',
        detail: 'GET /api/admin/assessments/:assessmentId',
        byUser: 'GET /api/admin/assessments/user/:userId'
      },
      configs: {
        list: 'GET /api/admin/configs',
        stats: 'GET /api/admin/configs/stats',
        detail: 'GET /api/admin/configs/:configKey',
        update: 'PATCH /api/admin/configs',
        create: 'POST /api/admin/configs',
        delete: 'DELETE /api/admin/configs/:configKey'
      }
    },
    authentication: {
      type: 'JWT Bearer Token',
      header: 'Authorization: Bearer <admin_token>',
      note: 'Use admin JWT token obtained from /api/admin/auth/login'
    }
  });
});

module.exports = router;
