const { query } = require('../config/db');

/**
 * Assessment Model for Admin Service
 * Handles database operations for assessment monitoring
 */
class AssessmentModel {
  /**
   * Get all assessments with pagination and filtering
   * @param {Object} options - Query options
   * @returns {Object} Assessments data with pagination
   */
  static async getAll(options = {}) {
    try {
      const { 
        limit = 20, 
        offset = 0, 
        status = '',
        user_id = '',
        from_date = '',
        to_date = '',
        orderBy = 'created_at',
        orderDirection = 'DESC'
      } = options;

      let whereConditions = [];
      let queryParams = [limit, offset];
      let paramIndex = 3;

      // Add status filter
      if (status) {
        whereConditions.push(`pp.status = $${paramIndex}`);
        queryParams.push(status);
        paramIndex++;
      }

      // Add user_id filter
      if (user_id) {
        whereConditions.push(`pp.user_id = $${paramIndex}`);
        queryParams.push(user_id);
        paramIndex++;
      }

      // Add date range filters
      if (from_date) {
        whereConditions.push(`pp.created_at >= $${paramIndex}`);
        queryParams.push(from_date);
        paramIndex++;
      }

      if (to_date) {
        whereConditions.push(`pp.created_at <= $${paramIndex}`);
        queryParams.push(to_date);
        paramIndex++;
      }

      const whereClause = whereConditions.length > 0 
        ? `WHERE ${whereConditions.join(' AND ')}`
        : '';

      // Get total count
      const countQuery = `
        SELECT COUNT(*) 
        FROM archive_schema.persona_profiles pp
        ${whereClause}
      `;
      const countParams = queryParams.slice(2); // Remove limit and offset
      const countResult = await query(countQuery, countParams);

      // Get assessments with user information
      const assessmentsQuery = `
        SELECT 
          pp.id,
          pp.user_id,
          pp.status,
          pp.raw_input_data,
          pp.persona_result,
          pp.error_message,
          pp.created_at,
          pp.completed_at,
          pp.updated_at,
          u.email as user_email,
          u.nama_lengkap as user_name,
          u.asal_sekolah as user_school
        FROM archive_schema.persona_profiles pp
        LEFT JOIN auth_schema.users u ON pp.user_id = u.id
        ${whereClause}
        ORDER BY pp.${orderBy} ${orderDirection}
        LIMIT $1 OFFSET $2
      `;

      const result = await query(assessmentsQuery, queryParams);

      return {
        data: result.rows,
        total: parseInt(countResult.rows[0].count),
        limit,
        offset,
        page: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit)
      };
    } catch (error) {
      console.error('Error getting all assessments:', error);
      throw error;
    }
  }

  /**
   * Get assessment by ID
   * @param {string} assessmentId - Assessment ID
   * @returns {Object|null} Assessment data or null if not found
   */
  static async getById(assessmentId) {
    try {
      const result = await query(`
        SELECT 
          pp.id,
          pp.user_id,
          pp.status,
          pp.raw_input_data,
          pp.persona_result,
          pp.error_message,
          pp.created_at,
          pp.completed_at,
          pp.updated_at,
          u.email as user_email,
          u.nama_lengkap as user_name,
          u.asal_sekolah as user_school
        FROM archive_schema.persona_profiles pp
        LEFT JOIN auth_schema.users u ON pp.user_id = u.id
        WHERE pp.id = $1
      `, [assessmentId]);

      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting assessment by ID:', error);
      throw error;
    }
  }

  /**
   * Get assessments by user ID
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Object} User's assessments
   */
  static async getByUserId(userId, options = {}) {
    try {
      const { limit = 10, offset = 0 } = options;

      const result = await query(`
        SELECT 
          pp.id,
          pp.status,
          pp.persona_result,
          pp.error_message,
          pp.created_at,
          pp.completed_at,
          pp.updated_at
        FROM archive_schema.persona_profiles pp
        WHERE pp.user_id = $1
        ORDER BY pp.created_at DESC
        LIMIT $2 OFFSET $3
      `, [userId, limit, offset]);

      return result.rows;
    } catch (error) {
      console.error('Error getting assessments by user ID:', error);
      throw error;
    }
  }

  /**
   * Get assessment statistics
   * @returns {Object} Assessment statistics
   */
  static async getStatistics() {
    try {
      const stats = await query(`
        SELECT 
          COUNT(*) as total_assessments,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_assessments,
          COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_assessments,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_assessments,
          COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as assessments_24h,
          COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as assessments_7d,
          COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as assessments_30d,
          AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time_seconds
        FROM archive_schema.persona_profiles
        WHERE status = 'completed'
      `);

      // Get daily assessment counts for the last 7 days
      const dailyStats = await query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
        FROM archive_schema.persona_profiles
        WHERE created_at >= NOW() - INTERVAL '7 days'
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `);

      return {
        ...stats.rows[0],
        daily_stats: dailyStats.rows
      };
    } catch (error) {
      console.error('Error getting assessment statistics:', error);
      throw error;
    }
  }

  /**
   * Get user assessment summary
   * @param {string} userId - User ID
   * @returns {Object} User assessment summary
   */
  static async getUserAssessmentSummary(userId) {
    try {
      const result = await query(`
        SELECT 
          COUNT(*) as total_assessments,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_assessments,
          COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_assessments,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_assessments,
          MAX(created_at) as last_assessment_date,
          MIN(created_at) as first_assessment_date
        FROM archive_schema.persona_profiles
        WHERE user_id = $1
      `, [userId]);

      return result.rows[0];
    } catch (error) {
      console.error('Error getting user assessment summary:', error);
      throw error;
    }
  }
}

module.exports = AssessmentModel;
