-- Database initialization script for Admin Service
-- This script sets up the necessary schemas, users, and tables for the Admin Service

-- Create the admin_service_user with appropriate permissions
CREATE USER admin_service_user WITH PASSWORD 'admin_service_password';

-- Create the admin_schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS admin_schema;

-- Grant permissions to admin_service_user on admin_schema
GRANT USAGE ON SCHEMA admin_schema TO admin_service_user;
GRANT CREATE ON SCHEMA admin_schema TO admin_service_user;

-- Grant permissions to access auth_schema (for users table)
GRANT USAGE ON SCHEMA auth_schema TO admin_service_user;
GRANT SELECT, UPDATE ON auth_schema.users TO admin_service_user;

-- Grant permissions to access archive_schema (for persona_profiles table)
GRANT USAGE ON SCHEMA archive_schema TO admin_service_user;
GRANT SELECT ON archive_schema.persona_profiles TO admin_service_user;

-- Create the admins table in auth_schema (keeping admin users with regular users schema)
CREATE TABLE IF NOT EXISTS auth_schema.admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'admin' CHECK (role IN ('admin', 'superadmin', 'support', 'viewer')),
    nama_lengkap VARCHAR(255) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Grant permissions on admins table
GRANT SELECT, INSERT, UPDATE ON auth_schema.admins TO admin_service_user;

-- Create the user_plans table in admin_schema
CREATE TABLE IF NOT EXISTS admin_schema.user_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth_schema.users(id) ON DELETE CASCADE,
    plan_type VARCHAR(50) NOT NULL DEFAULT 'free' CHECK (plan_type IN ('free', 'premium', 'enterprise')),
    analysis_quota INTEGER NOT NULL DEFAULT 5,
    used_quota INTEGER NOT NULL DEFAULT 0,
    quota_reset_date DATE,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create the system_configs table in admin_schema
CREATE TABLE IF NOT EXISTS admin_schema.system_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(50) NOT NULL DEFAULT 'string' CHECK (config_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_sensitive BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admins_email ON auth_schema.admins(email);
CREATE INDEX IF NOT EXISTS idx_admins_role ON auth_schema.admins(role);
CREATE INDEX IF NOT EXISTS idx_admins_is_active ON auth_schema.admins(is_active);

CREATE INDEX IF NOT EXISTS idx_user_plans_user_id ON admin_schema.user_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_user_plans_plan_type ON admin_schema.user_plans(plan_type);
CREATE INDEX IF NOT EXISTS idx_user_plans_is_active ON admin_schema.user_plans(is_active);

CREATE INDEX IF NOT EXISTS idx_system_configs_key ON admin_schema.system_configs(config_key);
CREATE INDEX IF NOT EXISTS idx_system_configs_type ON admin_schema.system_configs(config_type);

-- Grant table permissions to admin_service_user
GRANT SELECT, INSERT, UPDATE, DELETE ON admin_schema.user_plans TO admin_service_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON admin_schema.system_configs TO admin_service_user;

-- Create triggers to automatically update the updated_at column
CREATE OR REPLACE FUNCTION admin_schema.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables
CREATE TRIGGER update_admins_updated_at 
    BEFORE UPDATE ON auth_schema.admins 
    FOR EACH ROW 
    EXECUTE FUNCTION admin_schema.update_updated_at_column();

CREATE TRIGGER update_user_plans_updated_at 
    BEFORE UPDATE ON admin_schema.user_plans 
    FOR EACH ROW 
    EXECUTE FUNCTION admin_schema.update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at 
    BEFORE UPDATE ON admin_schema.system_configs 
    FOR EACH ROW 
    EXECUTE FUNCTION admin_schema.update_updated_at_column();

-- Insert default admin user (password: admin123)
-- Note: In production, change this password immediately
INSERT INTO auth_schema.admins (
    email, 
    password_hash, 
    role, 
    nama_lengkap
) VALUES (
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PJ/...',  -- bcrypt hash of 'admin123'
    'superadmin',
    'System Administrator'
) ON CONFLICT (email) DO NOTHING;

-- Insert default system configurations
INSERT INTO admin_schema.system_configs (config_key, config_value, config_type, description) VALUES
('llm_model', 'gpt-4', 'string', 'Default LLM model for assessments'),
('max_analysis_per_day', '100', 'number', 'Maximum analyses per day per user'),
('maintenance_mode', 'false', 'boolean', 'Enable/disable maintenance mode'),
('api_rate_limit', '60', 'number', 'API rate limit per minute per user'),
('assessment_timeout', '300', 'number', 'Assessment timeout in seconds')
ON CONFLICT (config_key) DO NOTHING;

-- Insert default user plans for existing users (if any)
INSERT INTO admin_schema.user_plans (user_id, plan_type, analysis_quota)
SELECT id, 'free', 5 
FROM auth_schema.users 
WHERE id NOT IN (SELECT user_id FROM admin_schema.user_plans)
ON CONFLICT (user_id) DO NOTHING;

-- Display success message
DO $$
BEGIN
    RAISE NOTICE 'Admin Service database initialization completed successfully!';
    RAISE NOTICE 'Schemas: auth_schema (admins), admin_schema (user_plans, system_configs)';
    RAISE NOTICE 'User: admin_service_user';
    RAISE NOTICE 'Default admin: <EMAIL> / admin123 (CHANGE THIS PASSWORD!)';
END $$;
